<?php
require_once 'config.php';

echo "<h1>🔍 Debug Member Upcoming Birthday Notification Templates</h1>";

try {
    // Get all Member Upcoming Birthday Notification templates
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    if (empty($templates)) {
        echo "<p>❌ No Member Upcoming Birthday Notification templates found</p>";
        
        // Show all templates to see what we have
        $stmt = $pdo->prepare("SELECT id, template_name, subject FROM email_templates ORDER BY template_name");
        $stmt->execute();
        $allTemplates = $stmt->fetchAll();
        
        echo "<h2>📋 All Available Templates:</h2>";
        echo "<ul>";
        foreach ($allTemplates as $template) {
            echo "<li><strong>ID " . $template['id'] . ":</strong> " . htmlspecialchars($template['template_name']) . "</li>";
            echo "<ul><li><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</li></ul>";
        }
        echo "</ul>";
        exit;
    }
    
    echo "<h2>📧 Member Upcoming Birthday Notification Templates Found:</h2>";
    
    foreach ($templates as $template) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h3>";
        echo "<p><strong>Subject:</strong> <code>" . htmlspecialchars($template['subject']) . "</code></p>";
        
        // Check if subject contains placeholders that might cause issues
        $hasImagePlaceholder = (
            strpos($template['subject'], '{member_image}') !== false ||
            strpos($template['subject'], '{birthday_member_image}') !== false ||
            strpos($template['subject'], '{member_photo}') !== false
        );
        
        if ($hasImagePlaceholder) {
            echo "<p style='color: red; font-weight: bold;'>⚠️ WARNING: Subject contains image placeholders that can cause HTML contamination!</p>";
        }
        
        // Show first 200 characters of content
        echo "<p><strong>Content Preview:</strong></p>";
        echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
        echo htmlspecialchars(substr($template['content'], 0, 200)) . "...";
        echo "</div>";
        echo "</div>";
    }
    
    // Test the problematic templates
    echo "<h2>🧪 Testing Template Processing</h2>";
    
    // Get Sandra's details for testing
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "<p>❌ Sandra not found for testing</p>";
        exit;
    }
    
    // Test member data for notifications
    $memberData = [
        'full_name' => $sandra['full_name'],
        'first_name' => explode(' ', $sandra['full_name'])[0],
        'email' => $sandra['email'],
        'birthday_member_name' => explode(' ', $sandra['full_name'])[0],
        'birthday_member_full_name' => $sandra['full_name'],
        'birthday_member_image' => 'uploads/685dbe1e2dec7.jpeg',
        '_is_birthday_notification' => true // This is key for notifications
    ];
    
    foreach ($templates as $template) {
        echo "<h3>🔍 Testing: " . htmlspecialchars($template['template_name']) . "</h3>";
        
        echo "<p><strong>Original Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        
        // Test current replaceTemplatePlaceholders function
        $processedSubject = replaceTemplatePlaceholders($template['subject'], $memberData, false, true);
        
        echo "<p><strong>Processed Subject:</strong> " . htmlspecialchars($processedSubject) . "</p>";
        
        // Check for HTML contamination
        $hasHtml = (
            strpos($processedSubject, '<') !== false ||
            strpos($processedSubject, 'style=') !== false ||
            strpos($processedSubject, 'margin') !== false ||
            strpos($processedSubject, 'padding') !== false ||
            strpos($processedSubject, 'font-family') !== false ||
            strpos($processedSubject, 'border') !== false ||
            strpos($processedSubject, 'display:') !== false
        );
        
        if ($hasHtml) {
            echo "<p style='color: red; font-weight: bold;'>❌ ISSUE: Subject contains HTML/CSS content!</p>";
            
            // Show what specific HTML/CSS was found
            if (strpos($processedSubject, 'style=') !== false) {
                echo "<p style='color: red;'>Found inline styles</p>";
            }
            if (strpos($processedSubject, 'margin') !== false) {
                echo "<p style='color: red;'>Found margin CSS</p>";
            }
            if (strpos($processedSubject, 'font-family') !== false) {
                echo "<p style='color: red;'>Found font-family CSS</p>";
            }
        } else {
            echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: Subject is clean!</p>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
