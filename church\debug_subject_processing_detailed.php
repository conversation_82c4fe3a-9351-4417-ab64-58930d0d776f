<?php
require_once 'config.php';

echo "<h1>🔍 Debug Subject Processing in Detail</h1>";

try {
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "<p>❌ Sandra not found</p>";
        exit;
    }
    
    // Get the notification templates
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    if (empty($templates)) {
        echo "<p>❌ No notification templates found</p>";
        exit;
    }
    
    echo "<h2>🧪 Testing Subject Processing Step by Step</h2>";
    
    foreach ($templates as $template) {
        echo "<h3>🔍 Testing: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h3>";
        echo "<p><strong>Original Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        
        // Test member data for notifications
        $memberData = [
            'full_name' => $sandra['full_name'],
            'first_name' => explode(' ', $sandra['full_name'])[0],
            'email' => $sandra['email'],
            'birthday_member_name' => explode(' ', $sandra['full_name'])[0],
            'birthday_member_full_name' => $sandra['full_name'],
            'birthday_member_image' => 'uploads/685dbe1e2dec7.jpeg',
            'days_text' => 'today',
            '_is_birthday_notification' => true
        ];
        
        echo "<h4>Step 1: Direct replaceTemplatePlaceholders test</h4>";
        $processedSubject1 = replaceTemplatePlaceholders($template['subject'], $memberData, false, true);
        echo "<p><strong>Result:</strong> " . htmlspecialchars($processedSubject1) . "</p>";
        
        echo "<h4>Step 2: Test with includes/email_functions.php sendEmail</h4>";
        
        // Clear log
        file_put_contents('logs/email_debug.log', '');
        
        // Test the sendEmail function from includes/email_functions.php
        require_once 'includes/email_functions.php';
        $result = sendEmail(
            $sandra['email'],
            $sandra['full_name'],
            $template['subject'],
            $template['content'],
            $memberData,
            true, // is birthday notification
            'birthday'
        );
        
        echo "<p><strong>sendEmail Result:</strong> " . ($result ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
        
        // Check the log for subject processing
        $logContent = file_get_contents('logs/email_debug.log');
        
        if (!empty($logContent)) {
            // Extract subject-related lines
            $lines = explode("\n", $logContent);
            $subjectLines = [];
            
            foreach ($lines as $line) {
                if (stripos($line, 'subject') !== false || 
                    stripos($line, 'SUBJECT') !== false ||
                    stripos($line, 'CLEAN') !== false ||
                    stripos($line, 'CONTAMINATION') !== false) {
                    $subjectLines[] = $line;
                }
            }
            
            if (!empty($subjectLines)) {
                echo "<h4>📋 Subject Processing Log:</h4>";
                echo "<ul>";
                foreach ($subjectLines as $line) {
                    echo "<li><code>" . htmlspecialchars($line) . "</code></li>";
                }
                echo "</ul>";
            }
            
            // Extract the actual SMTP subject
            if (preg_match('/Subject: (.+?)\\r?\\n/i', $logContent, $matches)) {
                $actualSubject = trim($matches[1]);
                echo "<h4>📤 Final SMTP Subject:</h4>";
                echo "<p style='background-color: #e7f3ff; padding: 10px; border-radius: 5px;'>";
                echo "<strong>Subject:</strong> <code>" . htmlspecialchars($actualSubject) . "</code>";
                echo "</p>";
            }
        }
        
        echo "<hr style='margin: 30px 0;'>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
