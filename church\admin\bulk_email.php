<?php
// First, check if this is an SSE request
$is_sse_request = isset($_GET['send_bulk_email']) || 
                  (isset($_SERVER['HTTP_ACCEPT']) && 
                   strpos($_SERVER['HTTP_ACCEPT'], 'text/event-stream') !== false);

// Prevent any output before headers
ob_start();

if ($is_sse_request) {
    // For SSE requests, we need clean output and proper headers
session_start();

    // Check authentication first
    if (!isset($_SESSION['admin_id'])) {
        header('HTTP/1.1 403 Forbidden');
        header('Content-Type: text/plain');
        exit('Not authorized');
    }
    
    // Clear any existing output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set SSE headers
    header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache, no-transform');
    header('Connection: keep-alive');
    header('X-Accel-Buffering: no');
    
    // Prevent Apache from buffering
    if (function_exists('apache_setenv')) {
        apache_setenv('no-gzip', '1');
    }
    
    // Send initial heartbeat
    echo "retry: 3000\n";
    echo ": heartbeat\n\n";
    flush();
    
    // Include required files after headers
    require_once '../config.php';
    require_once 'includes/language.php';
    $conn = $pdo;
} else {
    session_start();
    
    // Enable detailed error reporting for debugging purposes
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Database connection - using the connection from config.php
$conn = $pdo;
}

// Configure PHP settings for long-running process
set_time_limit(0); // No time limit
ini_set('max_execution_time', 0);
ini_set('memory_limit', '512M');
ini_set('max_input_time', -1);
ignore_user_abort(true);

// Enable detailed error reporting for debugging purposes
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set an exception handler to catch uncaught exceptions and display their details
set_exception_handler(function($exception) {
    echo "<pre>Uncaught Exception: " . $exception->getMessage() . "\nStack trace:\n" . $exception->getTraceAsString() . "</pre>";
});

// Register a shutdown function to catch fatal errors and other shutdown errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error) {
        echo "<pre>Shutdown Error: " . print_r($error, true) . "</pre>";
    }
});

$message = '';
$error = '';

// Handle email throttling settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_email_throttling'])) {
    try {
        // Validate inputs
        $email_sending_delay_seconds = isset($_POST['email_sending_delay_seconds']) ? max(1, min(60, intval($_POST['email_sending_delay_seconds']))) : 3;
        $email_batch_size = isset($_POST['email_batch_size']) ? max(10, min(5000, intval($_POST['email_batch_size']))) : 25;
        
        // Update email_settings table with the new values
        // Use INSERT ... ON DUPLICATE KEY UPDATE to handle both new and existing settings
        $settings = [
            'email_sending_delay_seconds' => $email_sending_delay_seconds,
            'email_batch_size' => $email_batch_size
        ];
        
        foreach ($settings as $key => $value) {
            // Check if setting exists first to avoid duplicate key error
            $stmt = $conn->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $exists = (int)$stmt->fetchColumn() > 0;
            
            if ($exists) {
                // Update existing setting
                $stmt = $conn->prepare("UPDATE email_settings SET setting_value = ? WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            } else {
                // Insert new setting
                $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?)");
                $stmt->execute([$key, $value]);
            }
        }
        
        $message = "Email throttling settings updated successfully.";
    } catch (Exception $e) {
        $error = "Failed to update email throttling settings: " . $e->getMessage();
        error_log("Error updating email throttling settings: " . $e->getMessage());
    }
}

// Get all bulk email templates
try {
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE template_category = 'bulk' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching templates: " . $e->getMessage();
}

// Include pagination component
require_once 'includes/pagination.php';

// Get members with pagination
try {
    // Default pagination values for members
    $membersPerPage = isset($_GET['members_per_page']) ? max(10, min(100, intval($_GET['members_per_page']))) : 20;
    $memberPage = isset($_GET['member_page']) ? max(1, intval($_GET['member_page'])) : 1;

    // Get total count of members
    $stmt = $conn->query("SELECT COUNT(*) FROM members");
    $totalMembers = $stmt->fetchColumn();

    // Calculate pagination for members
    $memberPagination = calculate_pagination($totalMembers, $memberPage, $membersPerPage);

    // Get paginated members
    $stmt = $conn->prepare("SELECT id, full_name, email, created_at FROM members ORDER BY full_name LIMIT ? OFFSET ?");
    $stmt->bindParam(1, $membersPerPage, PDO::PARAM_INT);
    $stmt->bindParam(2, $memberPagination['offset'], PDO::PARAM_INT);
    $stmt->execute();
    $members = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching members: " . $e->getMessage();
}

// Get contacts with pagination
try {
    // Default pagination values
    $contactsPerPage = isset($_GET['contacts_per_page']) ? intval($_GET['contacts_per_page']) : 50;
    $contactPage = isset($_GET['contact_page']) ? intval($_GET['contact_page']) : 1;
    $contactOffset = ($contactPage - 1) * $contactsPerPage;
    
    // Get total count of contacts
    $stmt = $conn->query("SELECT COUNT(*) FROM contacts");
    $totalContacts = $stmt->fetchColumn();
    $totalContactPages = ceil($totalContacts / $contactsPerPage);
    
    // Get paginated contacts
    $stmt = $conn->prepare("SELECT id, name as full_name, email, created_at FROM contacts ORDER BY name LIMIT ? OFFSET ?");
    $stmt->bindParam(1, $contactsPerPage, PDO::PARAM_INT);
    $stmt->bindParam(2, $contactOffset, PDO::PARAM_INT);
    $stmt->execute();
    $contacts = $stmt->fetchAll();
    
    $stmt = $conn->query("SELECT * FROM contact_groups ORDER BY name");
    $contact_groups = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching contacts: " . $e->getMessage();
}

// Ensure email throttling settings exist
try {
    // Check if email_sending_delay_seconds exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = 'email_sending_delay_seconds'");
    $stmt->execute();
    $delayExists = (int)$stmt->fetchColumn() > 0;
    
    // Check if email_batch_size exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = 'email_batch_size'");
    $stmt->execute();
    $batchSizeExists = (int)$stmt->fetchColumn() > 0;
    
    // Initialize settings if they don't exist
    if (!$delayExists) {
        $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('email_sending_delay_seconds', '3')");
        $stmt->execute();
    }
    
    if (!$batchSizeExists) {
        $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('email_batch_size', '25')");
        $stmt->execute();
    }
} catch (PDOException $e) {
    error_log("Error initializing email throttling settings: " . $e->getMessage());
}

// Get admin info for sender placeholders
$admin_info = [];
try {
    $stmt = $conn->prepare("SELECT * FROM admins WHERE id = ?");
    $stmt->execute([$_SESSION['admin_id']]);
    $admin_info = $stmt->fetch();
} catch (PDOException $e) {
    $error = "Error fetching admin info: " . $e->getMessage();
}

// At the very top of the file, add debugging output if needed
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("==== FORM SUBMISSION START ====");
    error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
    error_log("POST data: " . print_r($_POST, true));
    error_log("==== FORM SUBMISSION END ====");
}

// Helper function to send progress update
function send_progress_update($recipient, $status, $message = '', $current = 0, $total = 0) {
    $response = [
        'status' => $status,
        'recipient' => [
            'name' => $recipient['full_name'],
            'email' => $recipient['email']
        ],
        'message' => $message,
        'progress' => [
            'current' => $current,
            'total' => $total,
            'percentage' => $total > 0 ? round(($current / $total) * 100) : 0
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo "data: " . json_encode($response) . "\n\n";
    flush();
}

// Helper function to add random delay between emails
function add_sending_delay() {
    global $conn;
    
    // Get configured delay from email settings, default to 2 seconds if not set
    $delay_seconds = 2; // Default delay
    
    try {
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            $delay_seconds = floatval($result['setting_value']);
        }
    } catch (Exception $e) {
        // If there's an error, use the default delay
        error_log("Error fetching email delay setting: " . $e->getMessage());
    }
    
    // Add small random jitter to prevent exact patterns (0-0.5 seconds)
    $jitter = rand(0, 500) / 1000;
    
    // Convert to microseconds and sleep
    usleep(($delay_seconds + $jitter) * 1000000);
}

// Handle bulk email sending
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['send_bulk_email'])) {
    // Get form data
    $template_id = $_GET['template_id'] ?? null;
    $recipient_type = $_GET['recipient_type'] ?? 'members';
    $custom_subject = $_GET['custom_subject'] ?? '';
    $is_paused = isset($_GET['paused']) && $_GET['paused'] === '1';
    
    // Get batch size from settings, default to 50 if not set
    $batch_size = 50; // Default batch size
    try {
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            $batch_size = intval($result['setting_value']);
            // Ensure batch size is valid (between 10 and 5000)
            $batch_size = max(10, min(5000, $batch_size));
        }
    } catch (Exception $e) {
        error_log("Error fetching email batch size setting: " . $e->getMessage());
    }
    
    if (!$template_id) {
        send_progress_update(['full_name' => '', 'email' => ''], 'error', 'Please select an email template.');
        exit;
    }
    
    if (empty($_GET['selected_' . $recipient_type])) {
        send_progress_update(['full_name' => '', 'email' => ''], 'error', 'Please select at least one recipient.');
        exit;
    }
    
        try {
            // Get template details
            $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
            $stmt->execute([$template_id]);
            $template = $stmt->fetch();
            
            if (!$template) {
            send_progress_update(['full_name' => '', 'email' => ''], 'error', 'Selected template not found.');
            exit;
        }
        
        // Get selected recipients
        $selected_recipients = json_decode($_GET['selected_' . $recipient_type], true);
        
        // Initialize session progress if not exists
        if (!isset($_SESSION['bulk_email_progress'])) {
            $_SESSION['bulk_email_progress'] = [
                'last_index' => -1,
                'template_id' => $template_id,
                'recipient_type' => $recipient_type,
                'total_success' => 0,
                'total_failed' => 0
            ];
        }
        
        // Get total count of recipients
        $total_recipients = count($selected_recipients);
        
        // Send initial progress update
        send_progress_update(
            ['full_name' => '', 'email' => ''],
            'start',
            "Starting to send emails to $total_recipients recipients...",
            $_SESSION['bulk_email_progress']['last_index'] + 1,
            $total_recipients
        );
        
        // Process recipients in batches
        $current_batch = array_slice(
            $selected_recipients, 
            $_SESSION['bulk_email_progress']['last_index'] + 1, 
            $batch_size
        );
        
        if (empty($current_batch)) {
            // All recipients processed
            $success_count = $_SESSION['bulk_email_progress']['total_success'];
            $fail_count = $_SESSION['bulk_email_progress']['total_failed'];
            
            // Clear progress
            unset($_SESSION['bulk_email_progress']);
            
            send_progress_update(
                ['full_name' => '', 'email' => ''],
                'complete',
                "Completed: $success_count sent successfully, $fail_count failed.",
                $total_recipients,
                $total_recipients
            );
            exit;
        }
        
        // Fetch recipient details for current batch
        $placeholders = str_repeat('?,', count($current_batch) - 1) . '?';
        if ($recipient_type === 'members') {
            $stmt = $conn->prepare("SELECT id, full_name, email, phone_number FROM members WHERE id IN ($placeholders)");
        } else {
            $stmt = $conn->prepare("SELECT id, name as full_name, email FROM contacts WHERE id IN ($placeholders)");
        }
        $stmt->execute($current_batch);
        $recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Process each recipient in the current batch
        foreach ($recipients as $recipient) {
            $_SESSION['bulk_email_progress']['last_index']++;
            $current_count = $_SESSION['bulk_email_progress']['last_index'] + 1;
            
            try {
                // Check if process is paused
                if ($is_paused) {
                    send_progress_update(
                        $recipient,
                        'info',
                        "Process paused at recipient " . $current_count . " of $total_recipients",
                        $current_count,
                        $total_recipients
                    );
                    exit;
                }
                
                // Add placeholder data
                        $recipient['sender_name'] = $admin_info['full_name'] ?? 'Admin';
                        $recipient['sender_email'] = $admin_info['email'] ?? '';
                $recipient['total_recipients'] = $total_recipients;
                        $recipient['church_name'] = 'Freedom Assembly Church';
                        $recipient['recipient_full_name'] = $recipient['full_name'];
                        $recipient['recipient_first_name'] = explode(' ', $recipient['full_name'])[0];
                        $recipient['recipient_email'] = $recipient['email'];
                        $recipient['recipient_phone'] = $recipient['phone_number'] ?? '';
                        
                // Replace placeholders
                        $subject = !empty($custom_subject) ? $custom_subject : $template['subject'];
                        $subject = replaceTemplatePlaceholders($subject, $recipient, false, true); // Mark as subject line
                        $content = replaceTemplatePlaceholders($template['content'], $recipient);
                        
                        // Generate tracking ID
                        $tracking_id = uniqid('track_', true);
                
                // Start transaction for this recipient
                $conn->beginTransaction();
                        
                        if (sendEmail($recipient['email'], $recipient['full_name'], $subject, $content, true, $recipient)) {
                            // Log successful send
                            if ($recipient_type === 'members') {
                                $stmt = $conn->prepare("INSERT INTO email_logs (member_id, template_id, email_type, subject, status, sent_at) VALUES (?, ?, 'bulk', ?, 'success', NOW())");
                                $stmt->execute([$recipient['id'], $template_id, $subject]);
                                
                                $stmt = $conn->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'bulk', NOW())");
                                $stmt->execute([$recipient['id'], $tracking_id]);
                    }
                    
                    $_SESSION['bulk_email_progress']['total_success']++;
                    $conn->commit();
                    
                    send_progress_update(
                        $recipient,
                        'success',
                        "Email sent successfully",
                        $current_count,
                        $total_recipients
                    );
                        } else {
                            // Log failed send
                            if ($recipient_type === 'members') {
                                $stmt = $conn->prepare("INSERT INTO email_logs (member_id, template_id, email_type, subject, status, error_message, sent_at) VALUES (?, ?, 'bulk', ?, 'failed', ?, NOW())");
                                $stmt->execute([$recipient['id'], $template_id, $subject, $last_email_error ?? 'Unknown error']);
                    }
                    
                    $_SESSION['bulk_email_progress']['total_failed']++;
            $conn->commit();
            
                    send_progress_update(
                        $recipient,
                        'error',
                        $last_email_error ?? 'Failed to send email',
                        $current_count,
                        $total_recipients
                    );
                }
                
                // Add delay between emails
                add_sending_delay();
                
        } catch (Exception $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = "Error: " . $e->getMessage();
            error_log("Error in bulk email: " . $e->getMessage());
        }
        
        // Flush output after each recipient
        flush();
    }
    
    // Send progress update for batch completion
    send_progress_update(
        ['full_name' => '', 'email' => ''],
        'info',
        "Batch processed: " . count($recipients) . " recipients",
        $_SESSION['bulk_email_progress']['last_index'] + 1,
        $total_recipients
    );
    
} catch (Exception $e) {
    send_progress_update(
        ['full_name' => '', 'email' => ''],
        'error',
        "Error: " . $e->getMessage()
    );
}

exit; // End SSE stream
}

// Set page variables
$page_title = __('bulk_email');
$page_header = __('bulk_email');
$page_description = __('send_bulk_emails_description');

// Include header
include 'includes/header.php';

// Add custom CSS for contact groups display
echo '<style>
    .group-badges-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        max-width: 180px;
    }
    
    .table td .badge {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100px;
        display: inline-block;
    }
</style>';

// Display success message if it exists
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-envelope-fill me-2"></i><?php _e('send_bulk_email'); ?>
                </h5>
            </div>
            <div class="card-body">
                <!-- Email Throttling Settings -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-sliders me-2"></i><?php _e('email_throttling_settings'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" id="emailThrottlingForm" class="mb-0">
                            <input type="hidden" name="update_email_throttling" value="1">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email_sending_delay_seconds" class="form-label">Delay Between Emails (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="email_sending_delay_seconds" name="email_sending_delay_seconds" min="1" max="60" step="1" 
                                               value="<?php 
                                                  $delay = 3; // default
                                                  try {
                                                      $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds'");
                                                      $stmt->execute();
                                                      $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                                      if ($result && isset($result['setting_value'])) {
                                                          $delay = intval($result['setting_value']);
                                                      }
                                                  } catch (Exception $e) {}
                                                  echo $delay;
                                               ?>">
                                        <span class="input-group-text">seconds</span>
                                    </div>
                                    <div class="form-text">Recommended: 2-5 seconds. Higher values reduce the risk of being flagged as spam.</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email_batch_size" class="form-label">Batch Size</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="email_batch_size" name="email_batch_size" min="10" max="5000" step="5" 
                                               value="<?php 
                                                  $batch_size = 25; // default
                                                  try {
                                                      $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size'");
                                                      $stmt->execute();
                                                      $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                                      if ($result && isset($result['setting_value'])) {
                                                          $batch_size = intval($result['setting_value']);
                                                      }
                                                  } catch (Exception $e) {}
                                                  echo $batch_size;
                                               ?>">
                                        <span class="input-group-text">emails per batch</span>
                                    </div>
                                    <div class="form-text">Process emails in batches to manage server load. Smaller batches are easier to resume if interrupted.</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save me-1"></i>Save Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Instructions panel -->
                <div class="alert alert-info mb-4 instruction-panel">
                    <div class="d-flex justify-content-between align-items-start">
                        <h5><i class="bi bi-info-circle-fill me-2"></i><?php _e('how_to_send_bulk_emails'); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <ol class="mb-0">
                        <li>Select an email template or create a new one if needed</li>
                        <li>Choose recipients (members or contacts)</li>
                        <li>Customize the subject line (optional)</li>
                        <li>Preview the email before sending</li>
                        <li>Click "Send Emails" to begin the sending process</li>
                    </ol>
                    <div class="mt-2">
                        <strong>Note:</strong> Newsletter templates will not include member profile images to maintain a consistent appearance.
                    </div>
                </div>
                
                <form method="post" action="" id="bulkEmailForm">
                    <!-- Template Selection -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="template_id" class="form-label">Select Bulk Email Template</label>
                            <select class="form-select" id="template_id" name="template_id" required data-bs-toggle="tooltip" data-bs-placement="top" title="Choose a pre-designed email template. You can create new templates in the Email Templates section.">
                                <option value="">Choose a bulk email template...</option>
                                <?php if (count($templates) > 0): ?>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template['id']; ?>">
                                            <?php echo htmlspecialchars($template['template_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>No bulk email templates available. Please create one first.</option>
                                <?php endif; ?>
                            </select>
                            <?php if (count($templates) === 0): ?>
                                <div class="form-text text-danger">
                                    <i class="bi bi-exclamation-triangle-fill me-1"></i>
                                    No bulk email templates found. <a href="email_templates.php">Create a bulk email template</a> first.
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <label for="custom_subject" class="form-label">Custom Subject (Optional)</label>
                            <input type="text" class="form-control" id="custom_subject" name="custom_subject" 
                                   placeholder="Leave blank to use template subject" data-bs-toggle="tooltip" data-bs-placement="top" title="Customize the email subject line. If left blank, the template's default subject will be used.">
                            <div class="form-text">Supports placeholders like {full_name}, {church_name}</div>
                        </div>
                    </div>

                    <!-- Recipient Type Selection -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="recipientType" class="form-label">Select Recipient Type</label>
                            <select class="form-select" id="recipientType" name="recipient_type">
                                <option value="members">Members</option>
                                <option value="contacts">External Contacts</option>
                            </select>
                        </div>
                    </div>

                    <!-- Template Preview -->
                    <div class="mb-4">
                        <label class="form-label">Template Preview</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="templatePreview">
                                <p class="text-muted text-center">Select a template to see preview</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recipients Section -->
                    <div class="mb-4">
                        <h5 class="form-label"><?php _e('select_recipients'); ?></h5>
                        <!-- Contact Group Filter (hidden by default) -->
                        <div id="contactGroupFilter" class="mb-3" style="display: none;">
                            <label for="contactGroupSelect" class="form-label">Filter by Group</label>
                            <select class="form-select" id="contactGroupSelect">
                                <option value="">All Contacts</option>
                                <?php foreach ($contact_groups as $group): ?>
                                <option value="<?php echo $group['id']; ?>">
                                    <?php echo htmlspecialchars($group['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Member Selection -->
                    <div class="mb-4" id="memberSelection">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label">Select Recipients</label>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllMembers">
                                    <i class="bi bi-check-all me-1"></i>Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="50">
                                    Select 50
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="100">
                                    Select 100
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="250">
                                    Select 250
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="deselectAllMembers">
                                    <i class="bi bi-x-lg me-1"></i>Clear
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover" id="membersTable">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <input type="checkbox" class="form-check-input" id="selectAllMembersCheckbox">
                                        </th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Joined Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($members as $member): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input member-checkbox" 
                                                       name="selected_members[]" value="<?php echo $member['id']; ?>">
                                            </td>
                                            <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($member['email']); ?></td>
                                            <td><?php echo date('M j, Y', strtotime($member['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <!-- Enhanced Pagination for Members -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="selection-info" id="memberSelectionInfo">
                                    <span class="badge bg-primary">0 members selected</span>
                                </div>
                                <div>
                                    <?php if ($memberPagination['total_pages'] > 1): ?>
                                        <?php
                                        echo generate_pagination(
                                            $memberPagination['current_page'],
                                            $memberPagination['total_pages'],
                                            $memberPagination['total_records'],
                                            $memberPagination['records_per_page'],
                                            'bulk_email.php',
                                            [], // No additional URL parameters to preserve
                                            'member_page'
                                        );
                                        ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Selection (hidden by default) -->
                    <div class="mb-4" id="contactSelection" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <label class="form-label">Select Contacts</label>
                                <span class="badge bg-secondary ms-2" id="contactTotalBadge">Total: <?php echo $totalContacts; ?></span>
                                <small class="text-muted" id="contactPaginationText">Showing <?php echo count($contacts); ?> per page</small>
                            </div>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllContacts">
                                    <i class="bi bi-check-all me-1"></i>Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="loadContacts()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n-contacts" data-count="50">
                                    Select 50
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n-contacts" data-count="100">
                                    Select 100
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n-contacts" data-count="250">
                                    Select 250
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n-contacts" data-count="1000">
                                    Select 1000
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="deselectAllContacts">
                                    <i class="bi bi-x-lg me-1"></i>Clear
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover" id="contactsTable">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <input type="checkbox" class="form-check-input" id="selectAllContactsCheckbox">
                                        </th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Groups</th>
                                        <th>Added Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($contacts as $contact): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input contact-checkbox" 
                                                       name="selected_contacts[]" value="<?php echo $contact['id']; ?>">
                                            </td>
                                            <td><?php echo htmlspecialchars($contact['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($contact['email']); ?></td>
                                            <td>
                                                <div class="group-badges-container">
                                                <?php
                                                $stmt = $conn->prepare("SELECT g.name FROM contact_groups g 
                                                                      JOIN contact_group_members m ON g.id = m.group_id 
                                                                      WHERE m.contact_id = ?");
                                                $stmt->execute([$contact['id']]);
                                                $contact_groups = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                                
                                                // Limit the number of groups displayed to prevent UI clutter
                                                $max_groups_to_display = 3;
                                                $total_groups = count($contact_groups);
                                                
                                                if ($total_groups > 0) {
                                                    $displayed_groups = array_slice($contact_groups, 0, $max_groups_to_display);
                                                    foreach ($displayed_groups as $group) {
                                                        echo '<span class="badge bg-info me-1">' . htmlspecialchars($group) . '</span>';
                                                    }
                                                    
                                                    // Show count of remaining groups if any
                                                    if ($total_groups > $max_groups_to_display) {
                                                        $remaining = $total_groups - $max_groups_to_display;
                                                        echo '<span class="badge bg-secondary">+' . $remaining . ' more</span>';
                                                    }
                                                } else {
                                                    echo '<span class="badge bg-light text-muted">No groups</span>';
                                                }
                                                ?>
                                                </div>
                                            </td>
                                            <td><?php echo date('M j, Y', strtotime($contact['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <!-- Add Pagination for Contacts -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="selection-info" id="contactSelectionInfo">
                                    <span class="badge bg-primary">0 contacts selected</span>
                                </div>
                                <div>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-2" id="contactLimitSelect">
                                        <option value="10" <?php echo $contactsPerPage == 10 ? 'selected' : ''; ?>>10 per page</option>
                                        <option value="25" <?php echo $contactsPerPage == 25 ? 'selected' : ''; ?>>25 per page</option>
                                        <option value="50" <?php echo $contactsPerPage == 50 ? 'selected' : ''; ?>>50 per page</option>
                                        <option value="100" <?php echo $contactsPerPage == 100 ? 'selected' : ''; ?>>100 per page</option>
                                    </select>
                                    <nav aria-label="Contact pagination" class="d-inline-block">
                                        <ul class="pagination pagination-sm mb-0" id="contactPagination">
                                            <?php if ($contactPage > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link contact-page-link" href="#" data-page="<?php echo ($contactPage - 1); ?>">&laquo;</a>
                                                </li>
                                            <?php else: ?>
                                                <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
                                            <?php endif; ?>
                                            
                                            <?php
                                            $startPage = max(1, $contactPage - 2);
                                            $endPage = min($totalContactPages, $contactPage + 2);
                                            
                                            for ($i = $startPage; $i <= $endPage; $i++): ?>
                                                <li class="page-item <?php echo ($i == $contactPage) ? 'active' : ''; ?>">
                                                    <a class="page-link contact-page-link" href="#" data-page="<?php echo $i; ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($contactPage < $totalContactPages): ?>
                                                <li class="page-item">
                                                    <a class="page-link contact-page-link" href="#" data-page="<?php echo ($contactPage + 1); ?>">&raquo;</a>
                                                </li>
                                            <?php else: ?>
                                                <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary" id="sendButton" name="send_bulk_email">
                            <i class="bi bi-envelope-fill me-2"></i>Send Bulk Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel"><?php _e('sending_bulk_email'); ?></h5>
                <div class="ms-auto">
                    <button type="button" class="btn btn-warning btn-sm me-2" id="pauseResumeBtn">
                        <i class="bi bi-pause-fill"></i> Pause
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="stopBtn">
                        <i class="bi bi-stop-fill"></i> Stop
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <p class="mb-0" id="progressText">Preparing to send emails...</p>
                    <p class="mb-0" id="progressCount">0/0</p>
            </div>
                <div class="email-log-container border rounded p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                    <div id="emailLog"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeProgressBtn" style="display: none;">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container for Notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1080;">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="bi bi-info-circle me-2"></i>
            <strong class="me-auto" id="toastTitle">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage"></div>
    </div>
</div>

<script>
// Define these variables in the global scope to avoid initialization issues
let allAvailableMembers = [];
let allAvailableContacts = [];
let selectedMemberIds = [];
let selectedContactIds = [];
let currentContactPage = <?php echo $contactPage; ?>;
let currentContactSort = 'name';
let currentContactOrder = 'ASC';
let currentContactLimit = <?php echo $contactsPerPage; ?>;
let currentGroupId = '';

// Initialize from URL parameters if present
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.has('contacts_per_page')) {
    currentContactLimit = parseInt(urlParams.get('contacts_per_page')) || currentContactLimit;
}
if (urlParams.has('contact_page')) {
    currentContactPage = parseInt(urlParams.get('contact_page')) || currentContactPage;
}

// Update the contact limit selector to reflect current value
document.addEventListener('DOMContentLoaded', function() {
    const contactLimitSelect = document.getElementById('contactLimitSelect');
    if (contactLimitSelect) {
        contactLimitSelect.value = currentContactLimit;
        console.log('Set contact limit selector to:', currentContactLimit);
    }
});

// Function to sync contact limit
function syncContactLimit() {
    const contactLimitSelect = document.getElementById('contactLimitSelect');
    if (contactLimitSelect) {
        currentContactLimit = parseInt(contactLimitSelect.value) || 50;
        console.log('Synced contact limit to:', currentContactLimit);
    }
}

// Make variables accessible to contacts.js by attaching to window object
window.allAvailableMembers = allAvailableMembers;
window.allAvailableContacts = allAvailableContacts;
window.selectedMemberIds = selectedMemberIds;
window.selectedContactIds = selectedContactIds;

// Function to sync arrays with window object (for contacts.js compatibility)
function syncGlobalArrays() {
    window.allAvailableMembers = allAvailableMembers;
    window.allAvailableContacts = allAvailableContacts;
    window.selectedMemberIds = selectedMemberIds;
    window.selectedContactIds = selectedContactIds;
}

// Variables for member pagination and filtering
let currentMemberPage = 1;
let currentMemberSort = 'full_name';
let currentMemberOrder = 'ASC';
let currentMemberLimit = 10;
let currentMemberGroupId = '';

// Add these variables at the top of your script
let isPaused = false;
let isStopped = false;
let currentEventSource = null;

// Define the showNotification function
function showNotification(message, type = 'info') {
    console.log(`Notification (${type}): ${message}`);
    
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '5';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Initialize and show the toast
    const bsToast = new bootstrap.Toast(toast, { 
        animation: true,
        autohide: true,
        delay: 5000
    });
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// Define the updateContactsPagination function
function updateContactsPagination(pagination) {
    console.log('Updating contacts pagination with:', pagination);
    
    const paginationContainer = document.getElementById('contactsPagination');
    if (!paginationContainer) {
        console.error('Contacts pagination container not found');
        return;
    }
    
    // Calculate pages to show (show up to 5 pages)
    let startPage = Math.max(1, pagination.current - 2);
    let endPage = Math.min(pagination.pages, startPage + 4);
    
    // Adjust if we're showing less than 5 pages
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }
    
    let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">Showing ${pagination.start} to ${pagination.end} of ${pagination.total} contacts</small>
            </div>
            <ul class="pagination pagination-sm mb-0">
    `;
    
    // Previous button
    paginationHtml += `
        <li class="page-item ${pagination.current === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${pagination.current - 1}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === pagination.current ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHtml += `
        <li class="page-item ${pagination.current === pagination.pages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${pagination.current + 1}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    paginationHtml += `
            </ul>
        </div>
    `;
    
    paginationContainer.innerHTML = paginationHtml;
    
    // Add event listeners for pagination
    document.querySelectorAll('#contactsPagination .page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page >= 1 && page <= pagination.pages) {
                currentContactPage = page;
                loadContacts();
            }
        });
    });
}

// Load members function
function loadMembers() {
    console.log('Loading members with params:', {
        page: currentMemberPage,
        sort: currentMemberSort,
        order: currentMemberOrder,
        limit: currentMemberLimit
    });
    
    const url = `ajax/get_members.php?page=${currentMemberPage}&sort=${currentMemberSort}&order=${currentMemberOrder}&limit=${currentMemberLimit}&get_all=true`;
    console.log('Fetching members from:', url);
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            console.log('Members API response received');
            return response.json();
        })
        .then(data => {
            console.log('Members data loaded:', data);
            
            if (data.success) {
                const tbody = document.querySelector('#membersTable tbody');
                if (!tbody) {
                    console.error('Members table body not found in the DOM');
                    return;
                }
                
                tbody.innerHTML = '';
                
                // Store all members for bulk operations
                allAvailableMembers = data.all_members || [];
                console.log(`Loaded ${allAvailableMembers.length} total members, ${data.members.length} members for display`);
                
                // Populate table
                data.members.forEach((member, index) => {
                    const isSelected = selectedMemberIds.includes(member.id.toString());
                    
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>
                            <input type="checkbox" class="form-check-input member-checkbox" 
                                   name="selected_members[]" value="${member.id}" ${isSelected ? 'checked' : ''}>
                        </td>
                        <td>${member.full_name}</td>
                        <td>${member.email}</td>
                        <td>${new Date(member.created_at).toLocaleDateString()}</td>
                    `;
                    tbody.appendChild(tr);
                });
                
                // Update pagination
                updatePagination('members', currentMemberPage, Math.ceil(allAvailableMembers.length / currentMemberLimit));
                updateMemberSelectionInfo();
            } else {
                console.error('API returned error:', data.error || 'Unknown error');
                showNotification('Failed to load members: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error loading members:', error);
            showNotification('Error loading members: ' + error.message, 'error');
            
            // Show error in the table body
            const tbody = document.querySelector('#membersTable tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Error loading members: ${error.message}
                        </td>
                    </tr>
                `;
            }
        });
}

// Define the updateMembersPagination function
function updateMembersPagination(pagination) {
    console.log('Updating members pagination with:', pagination);
    
    const paginationContainer = document.getElementById('membersPagination');
    if (!paginationContainer) {
        console.error('Members pagination container not found');
        return;
    }
    
    // Calculate pages to show (show up to 5 pages)
    let startPage = Math.max(1, pagination.current - 2);
    let endPage = Math.min(pagination.pages, startPage + 4);
    
    // Adjust if we're showing less than 5 pages
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }
    
    let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">Showing ${pagination.start} to ${pagination.end} of ${pagination.total} members</small>
            </div>
            <ul class="pagination pagination-sm mb-0">
    `;
    
    // Previous button
    paginationHtml += `
        <li class="page-item ${pagination.current === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${pagination.current - 1}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === pagination.current ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHtml += `
        <li class="page-item ${pagination.current === pagination.pages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${pagination.current + 1}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    paginationHtml += `
            </ul>
        </div>
    `;
    
    paginationContainer.innerHTML = paginationHtml;
    
    // Add event listeners for pagination
    document.querySelectorAll('#membersPagination .page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page >= 1 && page <= pagination.pages) {
                currentMemberPage = page;
                loadMembers();
            }
        });
    });
}

// Define the loadContacts function in the global scope
function loadContacts() {
    // Sync contact limit from dropdown
    syncContactLimit();

    console.log('Loading contacts with params:', {
        page: currentContactPage,
        sort: currentContactSort,
        order: currentContactOrder,
        limit: currentContactLimit,
        group_id: currentGroupId
    });
    
    const url = `ajax/get_contacts.php?page=${currentContactPage}&sort=${currentContactSort}&order=${currentContactOrder}&limit=${currentContactLimit}&get_all=true&group_id=${currentGroupId}`;
    console.log('Fetching contacts from:', url);
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            console.log('Contacts API response received');
            return response.json();
        })
        .then(data => {
            console.log('Contacts data loaded:', data);
            
            if (data.success) {
                const tbody = document.querySelector('#contactsTable tbody');
                if (!tbody) {
                    console.error('Contacts table body not found in the DOM');
                    return;
                }
                
                tbody.innerHTML = '';
                
                // Store all contacts for bulk operations
                allAvailableContacts = data.all_contacts || [];
                console.log(`Loaded ${allAvailableContacts.length} total contacts, ${data.contacts.length} contacts for display`);

                // Sync with window object for contacts.js compatibility
                syncGlobalArrays();
                
                // Show warning if contacts are limited
                if (data.all_contacts_limited) {
                    const warningEl = document.getElementById('contactSelectionInfo');
                    if (warningEl) {
                        warningEl.innerHTML = `
                            <span class="badge bg-primary">${selectedContactIds.length} contacts selected</span>
                            <span class="badge bg-warning text-dark ms-2">Limited to ${allAvailableContacts.length} contacts</span>
                            <span class="small text-muted ms-2">Large contact list detected - using pagination</span>
                        `;
                    }
                    console.warn('Contact list has been limited to prevent memory issues');
                }
                
                // Populate table
                data.contacts.forEach((contact, index) => {
                    const isSelected = selectedContactIds.includes(contact.id.toString());
                    
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>
                            <input type="checkbox" class="form-check-input contact-checkbox" 
                                   name="selected_contacts[]" value="${contact.id}" ${isSelected ? 'checked' : ''}>
                        </td>
                        <td>${contact.name}</td>
                        <td>${contact.email}</td>
                        <td>${renderGroups(contact.groups)}</td>
                        <td>${new Date(contact.created_at).toLocaleDateString()}</td>
                    `;
                    tbody.appendChild(tr);
                });
                
                // Attach event listeners to newly created checkboxes
                tbody.querySelectorAll('.contact-checkbox').forEach(cb => {
                    cb.addEventListener('change', function() {
                        const contactId = this.value;
                        console.log('Contact checkbox changed:', contactId, this.checked);
                        
                        if (this.checked) {
                            // Add to selection if not already there
                            if (!selectedContactIds.includes(contactId)) {
                                selectedContactIds.push(contactId);
                            }
                        } else {
                            // Remove from selection
                            const index = selectedContactIds.indexOf(contactId);
                            if (index > -1) {
                                selectedContactIds.splice(index, 1);
                            }
                        }
                        
                        // Sync arrays with window object for contacts.js compatibility
                        syncGlobalArrays();

                        // Update selection info (using contacts.js function)
                        if (typeof updateSelectionInfo === 'function') {
                            updateSelectionInfo();
                        }
                        
                        // Update header checkbox
                        const checkboxes = document.querySelectorAll('.contact-checkbox');
                        const checkedCount = document.querySelectorAll('.contact-checkbox:checked').length;
                        const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
                        if (selectAllCheckbox) {
                            selectAllCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
                        }
                    });
                });
                
                // Update pagination display text
                updateContactPaginationText(data.pagination);

                // Update total badge
                const totalBadge = document.getElementById('contactTotalBadge');
                if (totalBadge) {
                    totalBadge.textContent = `Total: ${data.pagination.total}`;
                }

                // Update pagination using server data
                console.log('About to update pagination with:', data.pagination);
                if (data.pagination && data.pagination.pages > 0) {
                    updatePagination('contacts', data.pagination.current, data.pagination.pages);
                } else {
                    console.warn('Invalid pagination data:', data.pagination);
                    // Hide pagination if no pages
                    const paginationElement = document.getElementById('contactPagination');
                    if (paginationElement) {
                        paginationElement.innerHTML = '';
                    }
                }
                // Update selection info (using contacts.js function)
                if (typeof updateSelectionInfo === 'function') {
                    updateSelectionInfo();
                }
                
                // Update sort indicators
                document.querySelectorAll('.sort-contacts-link').forEach(link => {
                    const icon = link.querySelector('i');
                    if (link.dataset.sort === currentContactSort) {
                        icon.className = `bi bi-arrow-${currentContactOrder === 'ASC' ? 'up' : 'down'}`;
                    } else {
                        icon.className = 'bi bi-arrow-down-up';
                    }
                });

                // Update checkboxes based on stored selections
                document.querySelectorAll('.contact-checkbox').forEach(cb => {
                    cb.checked = selectedContactIds.includes(cb.value);
                });

                // Update header checkbox
                const visibleCheckboxes = document.querySelectorAll('.contact-checkbox');
                const allVisibleChecked = Array.from(visibleCheckboxes).every(cb => cb.checked);
                const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allVisibleChecked;
                }

                // Show selection status
                if (selectedContactIds.length > 0) {
                    showNotification(`${selectedContactIds.length} contacts selected across all pages`, 'info');
                }
            } else {
                console.error('API returned error:', data.error || 'Unknown error');
                showNotification('Failed to load contacts: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
            showNotification('Error loading contacts: ' + error.message, 'error');
            
            // Show error in the table body
            const tbody = document.querySelector('#contactsTable tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Error loading contacts: ${error.message}
                        </td>
                    </tr>
                `;
            }
        });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded - Initializing bulk email form');
    
    // Initialize form elements
    const templateSelect = document.getElementById('template_id');
    const templatePreview = document.getElementById('templatePreview');
    const recipientType = document.getElementById('recipientType');
    const memberSelection = document.getElementById('memberSelection');
    const contactSelection = document.getElementById('contactSelection');
    const contactGroupFilter = document.getElementById('contactGroupFilter');
    const contactGroupSelect = document.getElementById('contactGroupSelect');
    
    // Load initial data based on selected recipient type
    if (recipientType) {
        if (recipientType.value === 'members') {
            memberSelection.style.display = 'block';
            contactSelection.style.display = 'none';
            contactGroupFilter.style.display = 'none';
            loadMembers();
        } else {
            memberSelection.style.display = 'none';
            contactSelection.style.display = 'block';
            contactGroupFilter.style.display = 'block';
            // Small delay to ensure DOM is ready
            setTimeout(() => {
                loadContacts();
            }, 100);
        }
    }
    
    // Add event listeners for contact pagination links
    document.querySelectorAll('.contact-page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && !isNaN(page)) {
                console.log('Contact page link clicked:', page);
                currentContactPage = page;
                loadContacts();
                // Update selection info after contacts are loaded (using contacts.js function)
                setTimeout(() => {
                    if (typeof updateSelectionInfo === 'function') {
                        updateSelectionInfo();
                    }
                }, 200);
            }
        });
    });
    
    // Initialize selection buttons
    const selectAllContactsBtn = document.getElementById('selectAllContacts');
    const deselectAllContactsBtn = document.getElementById('deselectAllContacts');
    const selectAllContactsCheckbox = document.getElementById('selectAllContactsCheckbox');
    const selectAllMembersBtn = document.getElementById('selectAllMembers');
    const deselectAllMembersBtn = document.getElementById('deselectAllMembers');
    const selectAllMembersCheckbox = document.getElementById('selectAllMembersCheckbox');
    
    // Template Preview
    if (templateSelect && templatePreview) {
        console.log('Setting up template preview handler');
        
        templateSelect.addEventListener('change', function() {
            try {
                const templateId = this.value;
                console.log('Template selected:', templateId);
                
                if (templateId) {
                    console.log('Fetching template preview for:', templateId);
                    templatePreview.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
                    
                    fetch(`ajax/get_template_preview.php?id=${templateId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                console.log('Template preview loaded successfully');
                                templatePreview.innerHTML = `
                                    <div class="preview-container">
                                        <div class="preview-header mb-3">
                                            <strong>Subject:</strong> ${data.preview.subject}
                                        </div>
                                        <div class="preview-body">
                                            ${data.preview.content}
                                        </div>
                                    </div>
                                `;
                            } else {
                                throw new Error(data.error || 'Failed to load template preview');
                            }
                        })
                        .catch(error => {
                            console.error('Error loading template preview:', error);
                            templatePreview.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    Error loading preview: ${error.message}
                                </div>
                            `;
                        });
                } else {
                    templatePreview.innerHTML = '<p class="text-muted text-center">Select a template to see preview</p>';
                    console.log('No template selected, cleared preview');
                }
            } catch (error) {
                console.error('Error in template change handler:', error);
                showNotification('Error loading template preview: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Template select or preview container not found');
    }
    
    // Check initial state of recipient type and update UI accordingly
    if (recipientType) {
        console.log('Initial recipient type:', recipientType.value);
        
        // Set initial visibility
        if (recipientType.value === 'members') {
            if (memberSelection) memberSelection.style.display = 'block';
            if (contactSelection) contactSelection.style.display = 'none';
            if (contactGroupFilter) contactGroupFilter.style.display = 'none';
        } else {
            if (memberSelection) memberSelection.style.display = 'none';
            if (contactSelection) contactSelection.style.display = 'block';
            if (contactGroupFilter) contactGroupFilter.style.display = 'block';
            
            // Load contacts if needed
            loadContacts();
        }
    }
    
    // Handle recipient type change
    if (recipientType) {
        recipientType.addEventListener('change', function() {
            try {
                console.log('Recipient type changed to:', this.value);
                
                if (this.value === 'members') {
                    console.log('Showing member selection, hiding contact selection');
                    memberSelection.style.display = 'block';
                    contactSelection.style.display = 'none';
                    contactGroupFilter.style.display = 'none';
                    
                    // Load members if needed
                    if (!allAvailableMembers || allAvailableMembers.length === 0) {
                        loadMembers();
                    }
                } else {
                    console.log('Showing contact selection, hiding member selection');
                    memberSelection.style.display = 'none';
                    contactSelection.style.display = 'block';
                    contactGroupFilter.style.display = 'block';
                    
                    // Reset contact selection state
                    selectedContactIds = [];
                    currentContactPage = 1;
                    
                    // Load contacts
                    loadContacts();
                }
            } catch (error) {
                console.error('Error in recipient type change handler:', error);
                showNotification('Error switching recipient types: ' + error.message, 'error');
            }
        });
    }
    
    // Handle contact group filter
    if (contactGroupSelect) {
        console.log('Contact group select initialized with ID: contactGroupSelect');
        
        contactGroupSelect.addEventListener('change', function() {
            try {
                console.log('Group filter changed to:', this.value);
                currentGroupId = this.value;
                currentContactPage = 1; // Reset to first page when changing groups
                
                // Load contacts with new filter
                console.log('Reloading contacts with group ID:', currentGroupId);
                loadContacts();
            } catch (error) {
                console.error('Error in group filter change handler:', error);
                showNotification('Error filtering contacts: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Contact group select element not found with ID contactGroupSelect');
    }
    
    // Setup Select All Contacts button
    if (selectAllContactsBtn) {
        selectAllContactsBtn.addEventListener('click', function() {
            try {
                console.log('Select All Contacts button clicked');
                if (!allAvailableContacts || allAvailableContacts.length === 0) {
                    showNotification('No contacts available to select', 'warning');
                    return;
                }
                
                selectedContactIds = allAvailableContacts.map(contact => contact.id.toString());
                
                // Update visible checkboxes
                document.querySelectorAll('.contact-checkbox').forEach(cb => {
                    cb.checked = true;
                });
                
                // Update header checkbox
                const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = true;
                    selectAllCheckbox.indeterminate = false;
                }
                
                // Update selection info
                updateSelectionInfo();
                
                showNotification(`Selected all ${selectedContactIds.length} contacts`, 'info');
            } catch (error) {
                console.error('Error in Select All Contacts handler:', error);
                showNotification('Error selecting all contacts: ' + error.message, 'error');
            }
        });
    }
    
    // Setup Deselect All Contacts button
    if (deselectAllContactsBtn) {
        deselectAllContactsBtn.addEventListener('click', function() {
            try {
                console.log('Deselect All Contacts button clicked');
                // Uncheck all checkboxes
                document.querySelectorAll('.contact-checkbox').forEach(cb => {
                    cb.checked = false;
                });
                
                // Clear selected IDs
                selectedContactIds = [];
                
                // Update header checkbox
                if (selectAllContactsCheckbox) {
                    selectAllContactsCheckbox.checked = false;
                }
                
                // Update selection info display (using contacts.js function)
                if (typeof updateSelectionInfo === 'function') {
                    updateSelectionInfo();
                }
                
                showNotification('Cleared all selections', 'info');
                
                console.log('Cleared all contact selections');
            } catch (error) {
                console.error('Error in Deselect All Contacts handler:', error);
                showNotification('Error clearing selections: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Deselect All Contacts button not found');
    }
    
    // Setup Select First N Contacts buttons
    document.querySelectorAll('.select-first-n-contacts').forEach(button => {
        button.addEventListener('click', function() {
            try {
                const count = parseInt(this.dataset.count);
                console.log(`Select First ${count} Contacts button clicked`);
                
                if (!allAvailableContacts || allAvailableContacts.length === 0) {
                    showNotification('No contacts available to select', 'warning');
                    return;
                }
                
                if (count > allAvailableContacts.length) {
                    showNotification(`Only ${allAvailableContacts.length} contacts available (less than ${count})`, 'warning');
                }
                
                // Get IDs of the first N contacts
                selectedContactIds = allAvailableContacts
                    .slice(0, Math.min(count, allAvailableContacts.length))
                    .map(contact => contact.id.toString());
                
                // Update visible checkboxes
                document.querySelectorAll('.contact-checkbox').forEach(cb => {
                    cb.checked = selectedContactIds.includes(cb.value);
                });
                
                // Update header checkbox based on visible selections
                const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
                if (selectAllCheckbox) {
                    const visibleCheckboxes = document.querySelectorAll('.contact-checkbox');
                    const allVisibleChecked = Array.from(visibleCheckboxes).every(cb => selectedContactIds.includes(cb.value));
                    selectAllCheckbox.checked = allVisibleChecked;
                    selectAllCheckbox.indeterminate = !allVisibleChecked && selectedContactIds.length > 0;
                }
                
                // Update selection info
                updateSelectionInfo();
                
                showNotification(`Selected first ${selectedContactIds.length} contacts`, 'info');
            } catch (error) {
                console.error('Error in Select First N Contacts handler:', error);
                showNotification('Error selecting contacts: ' + error.message, 'error');
            }
        });
    });
    
    // Setup header checkbox for contacts
    if (selectAllContactsCheckbox) {
        selectAllContactsCheckbox.addEventListener('change', function() {
            try {
                console.log('Select All Contacts Checkbox changed:', this.checked);
                
                const checkboxes = document.querySelectorAll('.contact-checkbox');
                
                if (this.checked) {
                    // Select all visible checkboxes
                    checkboxes.forEach(cb => {
                        cb.checked = true;
                        if (!selectedContactIds.includes(cb.value)) {
                            selectedContactIds.push(cb.value);
                        }
                    });
                    
                    // Update selection info (using contacts.js function)
                    if (typeof updateSelectionInfo === 'function') {
                        updateSelectionInfo();
                    }
                    
                    showNotification(`Selected ${checkboxes.length} visible contacts`, 'info');
                } else {
                    // Uncheck all visible checkboxes and remove from selection
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                        const index = selectedContactIds.indexOf(cb.value);
                        if (index > -1) {
                            selectedContactIds.splice(index, 1);
                        }
                    });
                    
                    // Update selection info (using contacts.js function)
                    if (typeof updateSelectionInfo === 'function') {
                        updateSelectionInfo();
                    }
                    
                    showNotification('Cleared visible contact selections', 'info');
                }
                
                console.log(`Selected contacts count: ${selectedContactIds.length}`);
            } catch (error) {
                console.error('Error in Select All Contacts Checkbox handler:', error);
                showNotification('Error updating selections: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Select All Contacts Checkbox not found');
    }
    
    // Handle individual contact checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target && e.target.classList.contains('contact-checkbox')) {
            try {
                const contactId = e.target.value;
                console.log('Contact checkbox changed:', contactId, e.target.checked);
                
                if (e.target.checked) {
                    // Add to selection if not already there
                    if (!selectedContactIds.includes(contactId)) {
                        selectedContactIds.push(contactId);
                    }
                } else {
                    // Remove from selection
                    const index = selectedContactIds.indexOf(contactId);
                    if (index > -1) {
                        selectedContactIds.splice(index, 1);
                    }
                }
                
                // Update header checkbox based on visible selections
                const checkboxes = document.querySelectorAll('.contact-checkbox');
                const checkedCount = document.querySelectorAll('.contact-checkbox:checked').length;
                if (selectAllContactsCheckbox) {
                    selectAllContactsCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
                }
                
                console.log(`Selected contacts count: ${selectedContactIds.length}`);
            } catch (error) {
                console.error('Error in Contact Checkbox handler:', error);
                showNotification('Error updating selection: ' + error.message, 'error');
            }
        }
    });
    
    // Setup member selection buttons
    // Setup Select All Members button
    if (selectAllMembersBtn) {
        selectAllMembersBtn.addEventListener('click', function() {
            try {
                console.log('Select All Members button clicked');
                // Check all available members
                selectedMemberIds = allAvailableMembers.map(member => member.id.toString());
                
                // Update visible checkboxes
                document.querySelectorAll('.member-checkbox').forEach(cb => {
                    cb.checked = selectedMemberIds.includes(cb.value);
                });
                
                // Update header checkbox
                if (selectAllMembersCheckbox) {
                    selectAllMembersCheckbox.checked = true;
                }
                
                showNotification(`Selected all ${allAvailableMembers.length} members`, 'info');
                
                console.log(`Selected all ${allAvailableMembers.length} members`);
            } catch (error) {
                console.error('Error in Select All Members handler:', error);
                showNotification('Error selecting all members: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Select All Members button not found');
    }
    
    // Setup Deselect All Members button
    if (deselectAllMembersBtn) {
        deselectAllMembersBtn.addEventListener('click', function() {
            try {
                console.log('Deselect All Members button clicked');
                // Uncheck all checkboxes
                document.querySelectorAll('.member-checkbox').forEach(cb => {
                    cb.checked = false;
                });
                
                // Clear selected IDs
                selectedMemberIds = [];
                
                // Update header checkbox
                if (selectAllMembersCheckbox) {
                    selectAllMembersCheckbox.checked = false;
                }
                
                showNotification('Cleared all member selections', 'info');
                
                console.log('Cleared all member selections');
            } catch (error) {
                console.error('Error in Deselect All Members handler:', error);
                showNotification('Error clearing member selections: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Deselect All Members button not found');
    }
    
    // Setup Select First N Members buttons
    document.querySelectorAll('.select-first-n').forEach(button => {
        button.addEventListener('click', function() {
            try {
                const count = parseInt(this.dataset.count);
                console.log(`Select First ${count} Members button clicked`);
                
                if (!allAvailableMembers || allAvailableMembers.length === 0) {
                    showNotification('No members available to select', 'warning');
                    return;
                }
                
                if (count > allAvailableMembers.length) {
                    showNotification(`Only ${allAvailableMembers.length} members available (less than ${count})`, 'warning');
                }
                
                // Get IDs of the first N members
                selectedMemberIds = allAvailableMembers
                    .slice(0, Math.min(count, allAvailableMembers.length))
                    .map(member => member.id.toString());
                
                // Update visible checkboxes
                document.querySelectorAll('.member-checkbox').forEach(cb => {
                    cb.checked = selectedMemberIds.includes(cb.value);
                });
                
                // Update header checkbox based on visible selections
                const visibleCheckboxes = document.querySelectorAll('.member-checkbox');
                const allVisibleChecked = Array.from(visibleCheckboxes).every(cb => selectedMemberIds.includes(cb.value));
                if (selectAllMembersCheckbox) {
                    selectAllMembersCheckbox.checked = allVisibleChecked;
                }
                
                showNotification(`Selected ${selectedMemberIds.length} members across all pages`, 'info');
                
                console.log(`Selected first ${selectedMemberIds.length} members`);
            } catch (error) {
                console.error('Error in Select First N Members handler:', error);
                showNotification('Error selecting members: ' + error.message, 'error');
            }
        });
    });
    
    // Setup header checkbox for members
    if (selectAllMembersCheckbox) {
        selectAllMembersCheckbox.addEventListener('change', function() {
            try {
                console.log('Select All Members Checkbox changed:', this.checked);
                
                const checkboxes = document.querySelectorAll('.member-checkbox');
                
                if (this.checked) {
                    // Select all visible checkboxes
                    checkboxes.forEach(cb => {
                        cb.checked = true;
                        if (!selectedMemberIds.includes(cb.value)) {
                            selectedMemberIds.push(cb.value);
                        }
                    });
                    
                    showNotification(`Selected ${checkboxes.length} visible members`, 'info');
                } else {
                    // Uncheck all visible checkboxes and remove from selection
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                        const index = selectedMemberIds.indexOf(cb.value);
                        if (index > -1) {
                            selectedMemberIds.splice(index, 1);
                        }
                    });
                    
                    showNotification('Cleared visible member selections', 'info');
                }
                
                console.log(`Selected members count: ${selectedMemberIds.length}`);
            } catch (error) {
                console.error('Error in Select All Members Checkbox handler:', error);
                showNotification('Error updating member selections: ' + error.message, 'error');
            }
        });
    } else {
        console.error('Select All Members Checkbox not found');
    }
    
    // Handle individual member checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target && e.target.classList.contains('member-checkbox')) {
            try {
                const memberId = e.target.value;
                console.log('Member checkbox changed:', memberId, e.target.checked);
                
                if (e.target.checked) {
                    // Add to selection if not already there
                    if (!selectedMemberIds.includes(memberId)) {
                        selectedMemberIds.push(memberId);
                    }
                } else {
                    // Remove from selection
                    const index = selectedMemberIds.indexOf(memberId);
                    if (index > -1) {
                        selectedMemberIds.splice(index, 1);
                    }
                }
                
                // Update header checkbox based on visible selections
                const checkboxes = document.querySelectorAll('.member-checkbox');
                const checkedCount = document.querySelectorAll('.member-checkbox:checked').length;
                if (selectAllMembersCheckbox) {
                    selectAllMembersCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
                }
                
                console.log(`Selected members count: ${selectedMemberIds.length}`);
            } catch (error) {
                console.error('Error in Member Checkbox handler:', error);
                showNotification('Error updating member selection: ' + error.message, 'error');
            }
        }
    });
    
    // Load members when the page loads to initialize pagination
    if (recipientType && recipientType.value === 'members') {
        loadMembers();
    }
});

// After the DOMContentLoaded event listener, add the validation function
function validateFormElements() {
    console.log('Validating form elements...');
    
    const elements = {
        'templateSelect': document.getElementById('template_id'),
        'bulkEmailForm': document.getElementById('bulkEmailForm'),
        'recipientType': document.getElementById('recipientType'),
        'memberSelection': document.getElementById('memberSelection'),
        'contactSelection': document.getElementById('contactSelection'),
        'contactGroupFilter': document.getElementById('contactGroupFilter')
    };
    
    let hasErrors = false;
    for (const [name, element] of Object.entries(elements)) {
        if (!element) {
            console.error(`Element not found: ${name}`);
            hasErrors = true;
        } else {
            console.log(`Element found: ${name}`, element);
        }
    }
    
    if (hasErrors) {
        showNotification('Some form elements could not be found. See console for details.', 'error');
        return false;
    }
    
    // Check recipient type dropdown specifically
    if (elements.recipientType) {
        console.log('Recipient type value:', elements.recipientType.value);
        console.log('Recipient type options:', Array.from(elements.recipientType.options).map(o => ({value: o.value, text: o.text})));
    }
    
    // Check for member checkboxes only if we're in member mode
    if (elements.recipientType && elements.recipientType.value === 'members') {
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        console.log('Found member checkboxes:', memberCheckboxes.length);
        if (memberCheckboxes.length === 0) {
            console.warn('No member checkboxes found, but this might be normal if contacts are selected');
            // Don't return false here as this might be normal
        }
    }
    
    return true;
}

// Add a separate DOMContentLoaded event listener for the debug tools
document.addEventListener('DOMContentLoaded', function() {
    // Add validation call after page load
    setTimeout(validateFormElements, 1000);
    
    // Add debug tools to the page
    try {
        const bulkEmailForm = document.getElementById('bulkEmailForm');
        if (bulkEmailForm) {
            const debugDiv = document.createElement('div');
            debugDiv.className = 'mt-3 mb-3 p-3 bg-light border';
            debugDiv.innerHTML = `
                <h5>Debug Tools</h5>
                <div class="mb-2">
                    <button type="button" class="btn btn-sm btn-secondary me-2" id="validateFormBtn">
                        Validate Form Elements
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary me-2" id="toggleRecipientTypeBtn">
                        Toggle Recipient Type
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary" id="reloadContactsBtn">
                        Reload Contacts
                    </button>
                </div>
                <div id="debugOutput" class="small text-muted"></div>
            `;
            bulkEmailForm.after(debugDiv);
            
            // Add event listeners for debug buttons
            document.getElementById('validateFormBtn').addEventListener('click', function() {
                validateFormElements();
                document.getElementById('debugOutput').textContent = 'Form validation complete. Check console for details.';
            });
            
            document.getElementById('toggleRecipientTypeBtn').addEventListener('click', function() {
                const recipientType = document.getElementById('recipientType');
                if (recipientType) {
                    const newValue = recipientType.value === 'members' ? 'contacts' : 'members';
                    recipientType.value = newValue;
                    
                    // Manually trigger change event
                    const event = new Event('change');
                    recipientType.dispatchEvent(event);
                    
                    document.getElementById('debugOutput').textContent = `Recipient type set to: ${newValue}`;
                } else {
                    document.getElementById('debugOutput').textContent = 'Recipient type dropdown not found!';
                }
            });
            
            document.getElementById('reloadContactsBtn').addEventListener('click', function() {
                if (typeof loadContacts === 'function') {
                    loadContacts();
                    document.getElementById('debugOutput').textContent = 'Contacts reload triggered. Check console for details.';
                } else {
                    document.getElementById('debugOutput').textContent = 'loadContacts function not found!';
                }
            });
        } else {
            console.error('Could not find bulkEmailForm to attach debug tools');
        }
    } catch (error) {
        console.error('Error adding debug tools:', error);
    }
});

// Handle form submission
document.getElementById('bulkEmailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const recipientType = document.getElementById('recipientType').value;
    const selectedIds = recipientType === 'members' ? selectedMemberIds : selectedContactIds;
    
    if (selectedIds.length === 0) {
        alert('Please select at least one recipient.');
        return;
    }

    // Reset control flags
    isPaused = false;
    isStopped = false;
    
    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // Reset progress elements
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    const progressCount = document.getElementById('progressCount');
    const emailLog = document.getElementById('emailLog');
    const closeBtn = document.getElementById('closeProgressBtn');
    const pauseResumeBtn = document.getElementById('pauseResumeBtn');
    const stopBtn = document.getElementById('stopBtn');
    
    progressBar.style.width = '0%';
    progressBar.classList.remove('bg-success', 'bg-danger');
    progressBar.classList.add('progress-bar-animated', 'progress-bar-striped');
    emailLog.innerHTML = '';
    closeBtn.style.display = 'none';
    
    // Setup pause/resume button
    pauseResumeBtn.addEventListener('click', function() {
        isPaused = !isPaused;
        this.innerHTML = isPaused ? 
            '<i class="bi bi-play-fill"></i> Resume' : 
            '<i class="bi bi-pause-fill"></i> Pause';
        
        const logEntry = document.createElement('div');
        logEntry.className = 'mb-2 text-warning';
        logEntry.innerHTML = isPaused ? 
            '⏸️ Process paused by user' : 
            '▶️ Process resumed by user';
        emailLog.appendChild(logEntry);
        emailLog.scrollTop = emailLog.scrollHeight;
        
        // If resuming, recreate the EventSource
        if (!isPaused && currentEventSource === null) {
            createEventSource();
        }
    });
    
    // Setup stop button
    stopBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to stop the email sending process? This cannot be undone.')) {
            isStopped = true;
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }
            
            const logEntry = document.createElement('div');
            logEntry.className = 'mb-2 text-danger';
            logEntry.innerHTML = '⏹️ Process stopped by user';
            emailLog.appendChild(logEntry);
            emailLog.scrollTop = emailLog.scrollHeight;
            
            progressBar.classList.remove('progress-bar-animated', 'progress-bar-striped');
            progressBar.classList.add('bg-danger');
            closeBtn.style.display = 'block';
        }
    });
    
    // Create form data
    const formData = new FormData(form);
    formData.append(recipientType === 'members' ? 'selected_members' : 'selected_contacts', 
                   JSON.stringify(selectedIds));
    
    // Convert FormData to URLSearchParams
    const params = new URLSearchParams();
    for (const pair of formData.entries()) {
        params.append(pair[0], pair[1]);
    }
    
    let connectionAttempts = 0;
    const maxAttempts = 3;
    
    function createEventSource() {
        if (isPaused || isStopped) {
            return;
        }
        
        // Close existing connection if any
        if (currentEventSource) {
            currentEventSource.close();
            currentEventSource = null;
        }
        
        // Create URL for SSE connection
        const baseUrl = window.location.pathname;
        const sseUrl = `${baseUrl}?${params.toString()}&send_bulk_email=1${isPaused ? '&paused=1' : ''}`;
        
        console.log('Creating EventSource with URL:', sseUrl);
        
        try {
            // Create new EventSource with credentials
            currentEventSource = new EventSource(sseUrl, {
                withCredentials: true
            });
            
            // Set up event listeners
            currentEventSource.addEventListener('open', function(event) {
                console.log('SSE Connection opened successfully');
                connectionAttempts = 0; // Reset attempts on successful connection
                
                const logEntry = document.createElement('div');
                logEntry.className = 'mb-2 text-success';
                logEntry.innerHTML = '🔗 Connection established successfully';
                emailLog.appendChild(logEntry);
                emailLog.scrollTop = emailLog.scrollHeight;
            });
            
            currentEventSource.addEventListener('error', function(event) {
                console.error('EventSource error:', event);
                if (!isPaused && !isStopped) {
                    handleConnectionError();
                }
            });
            
            currentEventSource.addEventListener('message', function(event) {
                try {
                    if (isPaused) {
                        return;
                    }
                    
                    if (event.data === ': heartbeat') {
                        console.log('Received heartbeat');
                        return;
                    }
                    
                    const data = JSON.parse(event.data);
                    console.log('Received update:', data);
                    
                    // Update progress
                    if (data.progress) {
                        const percentage = data.progress.percentage;
                        requestAnimationFrame(() => {
                            progressBar.style.width = percentage + '%';
                            progressCount.textContent = `${data.progress.current}/${data.progress.total}`;
                        });
                    }
                    
                    // Create log entry
                    const logEntry = document.createElement('div');
                    logEntry.className = 'mb-2';
                    
                    switch (data.status) {
                        case 'start':
                            logEntry.innerHTML = `<span class="text-primary">🚀 ${data.message}</span>`;
                            break;
                        
                        case 'success':
                            logEntry.innerHTML = `<span class="text-success">✅ Processed email for ${data.recipient.name} (${data.recipient.email})</span>`;
                            break;
                        
                        case 'error':
                            logEntry.innerHTML = `<span class="text-danger">❌ Failed: ${data.recipient.name} (${data.recipient.email}) - ${data.message}</span>`;
                            break;
                        
                        case 'complete':
                            logEntry.innerHTML = `<span class="text-success">✨ ${data.message}</span>`;
                            progressBar.classList.remove('progress-bar-animated', 'progress-bar-striped');
                            progressBar.classList.add('bg-success');
                            closeBtn.style.display = 'block';
                            if (currentEventSource) {
                                currentEventSource.close();
                                currentEventSource = null;
                            }
                            break;
                    }
                    
                    emailLog.appendChild(logEntry);
                    requestAnimationFrame(() => {
                        emailLog.scrollTop = emailLog.scrollHeight;
                    });
                } catch (error) {
                    console.error('Error processing SSE message:', error);
                    const logEntry = document.createElement('div');
                    logEntry.className = 'mb-2 text-danger';
                    logEntry.innerHTML = `❌ Error processing update: ${error.message}`;
                    emailLog.appendChild(logEntry);
                    emailLog.scrollTop = emailLog.scrollHeight;
                }
            });
        } catch (error) {
            console.error('Error creating EventSource:', error);
            if (!isPaused && !isStopped) {
                handleConnectionError();
            }
        }
    }
    
    function handleConnectionError() {
        connectionAttempts++;
        console.error(`SSE Connection attempt ${connectionAttempts} failed`);
        
        if (connectionAttempts < maxAttempts && !isStopped) {
            const logEntry = document.createElement('div');
            logEntry.className = 'mb-2 text-warning';
            logEntry.innerHTML = `⚠️ Connection lost. Attempting to reconnect (${connectionAttempts}/${maxAttempts})...`;
            emailLog.appendChild(logEntry);
            emailLog.scrollTop = emailLog.scrollHeight;
            
            // Exponential backoff for retry
            setTimeout(createEventSource, Math.min(1000 * Math.pow(2, connectionAttempts - 1), 5000));
        } else {
            const logEntry = document.createElement('div');
            logEntry.className = 'mb-2 text-danger';
            logEntry.innerHTML = '❌ Connection failed after multiple attempts. Please try again.';
            emailLog.appendChild(logEntry);
            emailLog.scrollTop = emailLog.scrollHeight;
            
            progressBar.classList.remove('progress-bar-animated', 'progress-bar-striped');
            progressBar.classList.add('bg-danger');
            closeBtn.style.display = 'block';
            
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }
        }
    }
    
    // Initial connection
    createEventSource();
    
    // Handle close button
    closeBtn.addEventListener('click', function() {
        progressModal.hide();
        if (currentEventSource) {
            currentEventSource.close();
            currentEventSource = null;
        }
    });
    
    // Handle modal close
    document.getElementById('progressModal').addEventListener('hidden.bs.modal', function() {
        if (currentEventSource) {
            currentEventSource.close();
            currentEventSource = null;
        }
    });
});

// Add pagination functionality
function updatePagination(type, currentPage, totalPages) {
    console.log('updatePagination called:', { type, currentPage, totalPages });
    const paginationElementId = type === 'members' ? 'memberPagination' : 'contactPagination';
    const paginationElement = document.getElementById(paginationElementId);
    console.log('Looking for pagination element:', paginationElementId, 'Found:', !!paginationElement);
    if (!paginationElement) {
        console.error('Pagination element not found:', paginationElementId);
        return;
    }

    let html = '';
    
    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;

    // Calculate page range
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    // First page
    if (startPage > 1) {
        html += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="1">1</a>
            </li>
            ${startPage > 2 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
        `;
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }

    // Last page
    if (endPage < totalPages) {
        html += `
            ${endPage < totalPages - 1 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
            <li class="page-item">
                <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
            </li>
        `;
    }

    // Next button
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;

    paginationElement.innerHTML = html;

    // Add click handlers
    paginationElement.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && !isNaN(page)) {
                if (type === 'members') {
                    currentMemberPage = page;
                    loadMembers();
                } else {
                    currentContactPage = page;
                    loadContacts();
                }
                // Make sure the selection info is updated
                setTimeout(() => {
                    if (type === 'members') {
                        updateMemberSelectionInfo();
                    } else if (typeof updateSelectionInfo === 'function') {
                        updateSelectionInfo();
                    }
                }, 200);
            }
        });
    });
}

// Update contact pagination display text
function updateContactPaginationText(pagination) {
    const paginationTextElement = document.getElementById('contactPaginationText');
    if (paginationTextElement && pagination) {
        paginationTextElement.textContent = `Showing ${pagination.start}-${pagination.end} of ${pagination.total} contacts`;
    }
}

// Update selection info for members (contacts use the function from contacts.js)
function updateMemberSelectionInfo() {
    const infoElement = document.getElementById('memberSelectionInfo');
    if (!infoElement) return;

    const selectedCount = selectedMemberIds.length;
    const totalCount = allAvailableMembers.length;

    infoElement.innerHTML = `
        <span class="badge bg-primary">${selectedCount} members selected</span>
        <small class="text-muted ms-2">out of ${totalCount} total</small>
    `;
}

// Function to render contact groups with a limit to avoid UI clutter
function renderGroups(groups) {
    if (!groups || groups.length === 0) {
        return '<span class="badge bg-light text-muted">No groups</span>';
    }
    
    const maxGroupsToDisplay = 3;
    let html = '<div class="group-badges-container">';
    
    // Display first few groups
    const displayedGroups = groups.slice(0, maxGroupsToDisplay);
    displayedGroups.forEach(group => {
        html += `<span class="badge bg-info me-1">${group}</span>`;
    });
    
    // Show count of remaining groups if any
    if (groups.length > maxGroupsToDisplay) {
        const remaining = groups.length - maxGroupsToDisplay;
        html += `<span class="badge bg-secondary">+${remaining} more</span>`;
    }
    
    html += '</div>';
    return html;
}

// Add event listeners for page size selectors
document.getElementById('memberLimitSelect').addEventListener('change', function() {
    currentMemberLimit = parseInt(this.value);
    currentMemberPage = 1;
    loadMembers();
});

document.getElementById('contactLimitSelect').addEventListener('change', function() {
    currentContactLimit = parseInt(this.value);
    currentContactPage = 1;
    loadContacts();
});

// Update selection info when checkboxes change
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('member-checkbox')) {
        updateMemberSelectionInfo();
    } else if (e.target.classList.contains('contact-checkbox')) {
        if (typeof updateSelectionInfo === 'function') {
            updateSelectionInfo();
        }
    }
});

// Member pagination JavaScript
window.changePaginationLimit = function(newLimit) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('members_per_page', newLimit);
    urlParams.set('member_page', '1'); // Reset to first page
    window.location.href = 'bulk_email.php?' + urlParams.toString();
};

window.goToPage = function(pageNumber) {
    const totalPages = <?php echo isset($memberPagination['total_pages']) ? $memberPagination['total_pages'] : 1; ?>;
    pageNumber = parseInt(pageNumber);

    if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
        alert('Please enter a valid page number between 1 and ' + totalPages);
        return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('member_page', pageNumber);
    window.location.href = 'bulk_email.php?' + urlParams.toString();
};
</script>

<script src="js/contacts.js"></script>

<?php include 'includes/footer.php'; ?> 