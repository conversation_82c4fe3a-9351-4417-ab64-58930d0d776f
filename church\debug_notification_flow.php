<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h1>🔍 Debug Notification Template Flow</h1>";

// Clear logs
file_put_contents('logs/email_debug.log', '');

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "<p>❌ Sandra not found</p>";
        exit;
    }
    
    // Get the notification templates
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    if (empty($templates)) {
        echo "<p>❌ No notification templates found</p>";
        exit;
    }
    
    echo "<h2>📧 Testing Each Notification Template</h2>";
    
    foreach ($templates as $template) {
        echo "<h3>🔍 Testing: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h3>";
        echo "<p><strong>Original Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        
        // Test using the actual sendBirthdayEmails method
        echo "<p>Calling sendBirthdayEmails with this template...</p>";
        
        $result = $birthdayReminder->sendBirthdayEmails($template['id'], 0, true, [$sandra]);
        
        echo "<p><strong>Result:</strong></p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
        
        // Check the email debug log for this specific send
        $logContent = file_get_contents('logs/email_debug.log');
        $lines = explode("\n", $logContent);
        
        // Find SMTP subject lines for this send
        $smtpSubjects = [];
        foreach ($lines as $line) {
            if (strpos($line, 'Subject:') !== false && strpos($line, 'CLIENT -> SERVER:') !== false) {
                $smtpSubjects[] = $line;
            }
        }
        
        if (!empty($smtpSubjects)) {
            $lastSubject = end($smtpSubjects);
            echo "<p><strong>Actual SMTP Subject:</strong> <code>" . htmlspecialchars($lastSubject) . "</code></p>";
            
            // Check for HTML contamination
            $hasHtml = (
                strpos($lastSubject, 'body {') !== false ||
                strpos($lastSubject, 'margin:') !== false ||
                strpos($lastSubject, 'font-family') !== false ||
                strpos($lastSubject, 'style=') !== false ||
                strpos($lastSubject, 'padding:') !== false ||
                strpos($lastSubject, 'border:') !== false ||
                strpos($lastSubject, 'display:') !== false ||
                strpos($lastSubject, 'background') !== false
            );
            
            if ($hasHtml) {
                echo "<p style='color: red; font-weight: bold;'>❌ CONTAMINATED: Subject contains HTML/CSS!</p>";
                
                // Show specific contamination
                if (strpos($lastSubject, 'body {') !== false) {
                    echo "<p style='color: red;'>• Found 'body {' CSS</p>";
                }
                if (strpos($lastSubject, 'margin:') !== false) {
                    echo "<p style='color: red;'>• Found 'margin:' CSS</p>";
                }
                if (strpos($lastSubject, 'font-family') !== false) {
                    echo "<p style='color: red;'>• Found 'font-family' CSS</p>";
                }
                if (strpos($lastSubject, 'style=') !== false) {
                    echo "<p style='color: red;'>• Found inline 'style=' attributes</p>";
                }
            } else {
                echo "<p style='color: green; font-weight: bold;'>✅ CLEAN: Subject is free of HTML/CSS!</p>";
            }
        } else {
            echo "<p>❌ No SMTP subject found in logs</p>";
        }
        
        echo "<hr>";
        
        // Clear log for next test
        file_put_contents('logs/email_debug.log', '');
    }
    
    // Now let's test what member data is being used
    echo "<h2>🔍 Analyzing Member Data for Notifications</h2>";
    
    // Check what member data is being passed to the template processing
    echo "<p>Let's see what data is available for Sandra:</p>";
    echo "<pre>";
    print_r($sandra);
    echo "</pre>";
    
    // Check if there are any image-related fields that might be causing issues
    $imageFields = [];
    foreach ($sandra as $key => $value) {
        if (strpos($key, 'image') !== false || strpos($key, 'photo') !== false || strpos($key, 'picture') !== false) {
            $imageFields[$key] = $value;
        }
    }
    
    if (!empty($imageFields)) {
        echo "<h3>🖼️ Image-related fields found:</h3>";
        echo "<pre>";
        print_r($imageFields);
        echo "</pre>";
    }
    
    // Test the replaceTemplatePlaceholders function directly with notification data
    echo "<h2>🧪 Direct Template Processing Test</h2>";
    
    $notificationData = [
        'full_name' => $sandra['full_name'],
        'first_name' => explode(' ', $sandra['full_name'])[0],
        'email' => $sandra['email'],
        'birthday_member_name' => explode(' ', $sandra['full_name'])[0],
        'birthday_member_full_name' => $sandra['full_name'],
        'birthday_member_image' => 'uploads/685dbe1e2dec7.jpeg',
        'days_text' => 'today',
        '_is_birthday_notification' => true
    ];
    
    foreach ($templates as $template) {
        echo "<h3>Testing: " . htmlspecialchars($template['template_name']) . "</h3>";
        echo "<p><strong>Original:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        
        $processed = replaceTemplatePlaceholders($template['subject'], $notificationData, false, true);
        echo "<p><strong>Processed:</strong> " . htmlspecialchars($processed) . "</p>";
        
        $hasHtml = (strpos($processed, '<') !== false || strpos($processed, 'style=') !== false);
        echo "<p><strong>Status:</strong> " . ($hasHtml ? "❌ CONTAMINATED" : "✅ CLEAN") . "</p>";
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
