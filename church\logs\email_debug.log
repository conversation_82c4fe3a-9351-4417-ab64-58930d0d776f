[2025-07-19 21:11:45] Global sendEmail called for <PERSON> <<EMAIL>>
[2025-07-19 21:11:45] Initial email body length: 2518 characters
[2025-07-19 21:11:45] Email type: Regular
[2025-07-19 21:11:45] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 21:11:45] Set Reply-To address: <EMAIL>
[2025-07-19 21:11:45] Set custom headers and Message-ID for better deliverability
[2025-07-19 21:11:45] Ultra-clean subject set: 'Join Us in Celebrating birthday_member_full_name!'
[2025-07-19 21:11:45] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 21:11:45] SYSTEMATIC FIX: No images found in HTML body
[2025-07-19 21:11:45] SYSTEMATIC FIX: Image processing complete
[2025-07-19 21:11:45]   - Email type: Regular
[2025-07-19 21:11:45]   - Images found in HTML: 0
[2025-07-19 21:11:45]   - Images successfully embedded: 0
[2025-07-19 21:11:45]   - Processed image URLs: 0
[2025-07-19 21:11:45]   - Processed image paths: 0
[2025-07-19 21:11:45] Set HTML body (2518 chars) and plain text alternative (1221 chars)
[2025-07-19 21:11:45] Attempting to send email to: <EMAIL> with subject: Join Us in Celebrating {birthday_member_full_name}!
[2025-07-19 21:11:45] Email content validation: Body length=2518, HTML=Yes
[2025-07-19 21:11:45] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 21:11:45] SMTP Debug [1]: CLIENT -> SERVER: EHLO USER

[2025-07-19 21:11:45] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 21:11:45] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 21:11:46] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 21:11:46] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 21:11:46] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 21:11:46] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 21:11:46] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 21:11:46] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 21:11:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 21:11:46] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 21:11:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 21:11:46] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 21:11:47] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 21:11:45 +0200

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: To: Sandra Stern <<EMAIL>>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Subject: Join Us in Celebrating birthday_member_full_name!

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_rcjFPK4EQq4kjiAMumDyTZpWei1a256sUXbYxxvMhkM"

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: --b1=_rcjFPK4EQq4kjiAMumDyTZpWei1a256sUXbYxxvMhkM

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Birthday Celebration body { font-family: Arial, sans-serif; margin: 0;

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: padding: 20px; background: #f5f5f5; } .container { max-width: 600px; margin=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: :

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 0

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 4px 8px rgba(0,0,0,0.1); } .header { text-align: center; color: #ff4081;

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: margin-bottom: 30px; } .member-section { text-align: center; margin: 30px 0=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ;

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: } .member-name { font-size: 24px; font-weight: bold; color: #6a0dad; margin=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: :

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 15px 0; } .details { font-size: 16px; margin: 10px 0; } .footer {

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: text-align: center; margin-top: 30px; color: #666; } =F0=9F=8E=88 Birthday

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Celebration Time! =F0=9F=8E=88 Let's come together and celebrate this wonde=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: rful day!

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=82 Dear {recipient_full_name}, =F0=9F=8E=82 We are excited to cel=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ebrate

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: {birthday_member_full_name}'s birthday {days_text}! =F0=9F=8E=89 {member_im=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: age} =F0=9F=8E=82

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: {birthday_member_full_name} =F0=9F=8E=82 =F0=9F=8E=88 Birthday:

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: {upcoming_birthday_formatted} =F0=9F=8E=82 Turning: {birthday_member_age} y=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ears =F0=9F=8E=89

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Celebration is {days_text}! =F0=9F=8E=81 Ways to Celebrate

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: {birthday_member_full_name}: =F0=9F=92=8C Send a heartfelt birthday message=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:  =F0=9F=99=8F

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Pray for their growth and blessings =F0=9F=93=96 Share an inspiring scriptu=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: re =F0=9F=8E=81

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Consider a meaningful gift =F0=9F=8E=89 Send Birthday Wishes With love from

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: {organization_name} =F0=9F=92=92

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: --b1=_rcjFPK4EQq4kjiAMumDyTZpWei1a256sUXbYxxvMhkM

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>=0A<html>=0A<head>=0A    <meta charset=3D"UTF-8">=0A    <tit=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: le>Birthday Celebration</title>=0A    <style>=0A        body { font-family:=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:  Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }=0A    =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:     .container { max-width: 600px; margin: 0 auto; background: white; borde=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: r-radius: 10px; padding: 30px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }=0A =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:        .header { text-align: center; color: #ff4081; margin-bottom: 30px; }=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =0A        .member-section { text-align: center; margin: 30px 0; }=0A      =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:   .member-name { font-size: 24px; font-weight: bold; color: #6a0dad; margin=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: : 15px 0; }=0A        .details { font-size: 16px; margin: 10px 0; }=0A     =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:    .footer { text-align: center; margin-top: 30px; color: #666; }=0A    </s=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: tyle>=0A</head>=0A<body>=0A    <div class=3D"container">=0A        <div cla=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ss=3D"header">=0A            <h1>=F0=9F=8E=88 Birthday Celebration Time! =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=88</h1>=0A            <h3>Let's come together and celebrate this =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: wonderful day! =F0=9F=8E=82</h3>=0A        </div>=0A        =0A        <p>D=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ear {recipient_full_name},</p>=0A        =0A        <p>=F0=9F=8E=82 We are =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: excited to celebrate <strong>{birthday_member_full_name}'s</strong> birthda=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: y {days_text}! =F0=9F=8E=89</p>=0A        =0A        <div class=3D"member-s=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ection">=0A            {member_image}=0A            <div class=3D"member-na=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: me">=F0=9F=8E=82 {birthday_member_full_name} =F0=9F=8E=82</div>=0A        <=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: /div>=0A        =0A        <div class=3D"details">=0A            <p>=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=88 <strong>Birthday:</strong> {upcoming_birthday_formatted}</p>=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =0A            <p>=F0=9F=8E=82 <strong>Turning:</strong> {birthday_member_a=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ge} years</p>=0A            <p>=F0=9F=8E=89 <strong>Celebration is {days_te=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: xt}!</strong></p>=0A        </div>=0A        =0A        <div style=3D"margi=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: n: 20px 0;">=0A            <h4>=F0=9F=8E=81 Ways to Celebrate {birthday_mem=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ber_full_name}:</h4>=0A            <ul>=0A                <li>=F0=9F=92=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =8C Send a heartfelt birthday message</li>=0A                <li>=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=99=8F Pray for their growth and blessings</li>=0A                <li=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: >=F0=9F=93=96 Share an inspiring scripture</li>=0A                <li>=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=81 Consider a meaningful gift</li>=0A            </ul>=0A        =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: </div>=0A        =0A        <div style=3D"text-align: center; margin: 30px =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 0;">=0A            <a href=3D"mailto:{birthday_member_email}?subject=3DHapp=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: y%20Birthday%20{birthday_member_first_name}!" =0A               style=3D"ba=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ckground: #ff4081; color: white; padding: 12px 24px; text-decoration: none;=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER:  border-radius: 25px; font-weight: bold;">=0A            =F0=9F=8E=89 Send =

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: Birthday Wishes</a>=0A        </div>=0A        =0A        <div class=3D"foo=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: ter">=0A            <p>With love from {organization_name} =F0=9F=92=92</p>=

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: =0A        </div>=0A    </div>=0A</body>=0A</html>

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: --b1=_rcjFPK4EQq4kjiAMumDyTZpWei1a256sUXbYxxvMhkM--

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 21:11:47] SMTP Debug [1]: CLIENT -> SERVER: .

